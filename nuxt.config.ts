// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },

  // إعدادات CSS و Tailwind
  css: ['~/assets/css/main.css'],

  // الوحدات المطلوبة
  modules: [
    '@nuxtjs/tailwindcss',
    '@pinia/nuxt',
    '@nuxtjs/i18n'
  ],

  // إعدادات Tailwind CSS
  tailwindcss: {
    cssPath: '~/assets/css/main.css',
    configPath: 'tailwind.config.js',
    exposeConfig: false,
    config: {},
    injectPosition: 0,
    viewer: true,
  },

  // إعدادات تعدد اللغات
  i18n: {
    locales: [
      { code: 'ar', name: 'العربية', file: 'ar.json', dir: 'rtl' },
      { code: 'en', name: 'English', file: 'en.json', dir: 'ltr' }
    ],
    lazy: true,
    langDir: 'locales/',
    defaultLocale: 'ar',
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root'
    }
  },

  // إعدادات الرأس
  app: {
    head: {
      title: 'الذِّكر الحكيم - موقع إسلامي متكامل',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: 'موقع إسلامي تعليمي وتفاعلي يعرض القرآن الكريم كاملاً مع خدمات دينية متنوعة' },
        { name: 'keywords', content: 'القرآن الكريم, الذكر الحكيم, إسلام, قراءة القرآن, تفسير, أذكار' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
        { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: '' },
        { rel: 'stylesheet', href: 'https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap' }
      ]
    }
  },

  // إعدادات الـ SSR
  ssr: true,

  // إعدادات الـ Runtime
  runtimeConfig: {
    public: {
      siteName: 'الذِّكر الحكيم',
      siteDescription: 'موقع إسلامي متكامل وفاخر'
    }
  }
})
