<template>
  <div class="min-h-screen">
    <!-- قسم الترحيب الرئيسي -->
    <section class="relative py-20 overflow-hidden">
      <!-- خلفية متحركة -->
      <div class="absolute inset-0 islamic-pattern opacity-30"></div>
      
      <div class="relative container mx-auto text-center">
        <!-- العنوان الرئيسي -->
        <div class="mb-12" data-aos="fade-up">
          <h1 class="text-4xl md:text-6xl font-bold text-gradient font-arabic mb-4">
            {{ $t('home.welcome') }}
          </h1>
          <p class="text-xl md:text-2xl text-slate-600 dark:text-slate-400 mb-6">
            {{ $t('home.subtitle') }}
          </p>
          <p class="text-lg text-slate-600 dark:text-slate-400 max-w-3xl mx-auto leading-relaxed">
            {{ $t('home.description') }}
          </p>
        </div>

        <!-- زر استكشاف الخدمات -->
        <div class="mb-16" data-aos="fade-up" data-aos-delay="200">
          <NuxtLink to="/services" class="btn-gold inline-flex items-center space-x-2 rtl:space-x-reverse">
            <span>{{ $t('home.exploreServices') }}</span>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
            </svg>
          </NuxtLink>
        </div>
      </div>
    </section>

    <!-- قسم إحصائيات القرآن الكريم -->
    <section class="py-16">
      <div class="container mx-auto">
        <div class="text-center mb-12" data-aos="fade-up">
          <h2 class="text-3xl md:text-4xl font-bold text-slate-800 dark:text-slate-200 font-arabic mb-4">
            القرآن الكريم في أرقام
          </h2>
          <p class="text-lg text-slate-600 dark:text-slate-400">
            إحصائيات مباركة من كتاب الله العزيز
          </p>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- عدد السور -->
          <div class="stat-card" data-aos="fade-up" data-aos-delay="100">
            <div class="stat-number">{{ $t('home.stats.suras.number') }}</div>
            <div class="stat-label">{{ $t('home.stats.suras.label') }}</div>
            <div class="mt-4">
              <svg class="w-12 h-12 mx-auto text-gold-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"/>
              </svg>
            </div>
          </div>

          <!-- عدد الآيات -->
          <div class="stat-card" data-aos="fade-up" data-aos-delay="200">
            <div class="stat-number">{{ $t('home.stats.verses.number') }}</div>
            <div class="stat-label">{{ $t('home.stats.verses.label') }}</div>
            <div class="mt-4">
              <svg class="w-12 h-12 mx-auto text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
              </svg>
            </div>
          </div>

          <!-- عدد الأجزاء -->
          <div class="stat-card" data-aos="fade-up" data-aos-delay="300">
            <div class="stat-number">{{ $t('home.stats.parts.number') }}</div>
            <div class="stat-label">{{ $t('home.stats.parts.label') }}</div>
            <div class="mt-4">
              <svg class="w-12 h-12 mx-auto text-green-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19Z"/>
              </svg>
            </div>
          </div>

          <!-- عدد الصفحات -->
          <div class="stat-card" data-aos="fade-up" data-aos-delay="400">
            <div class="stat-number">{{ $t('home.stats.pages.number') }}</div>
            <div class="stat-label">{{ $t('home.stats.pages.label') }}</div>
            <div class="mt-4">
              <svg class="w-12 h-12 mx-auto text-purple-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M16,9H8V7H16M16,13H8V11H16M16,17H8V15H16M6,2A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2H6Z"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- قسم الخدمات المميزة -->
    <section class="py-16 bg-gradient-to-br from-blue-50/50 to-purple-50/50 dark:from-slate-800/50 dark:to-slate-900/50">
      <div class="container mx-auto">
        <div class="text-center mb-12" data-aos="fade-up">
          <h2 class="text-3xl md:text-4xl font-bold text-slate-800 dark:text-slate-200 font-arabic mb-4">
            خدماتنا المميزة
          </h2>
          <p class="text-lg text-slate-600 dark:text-slate-400">
            مجموعة شاملة من الخدمات الإسلامية لتعزيز رحلتك الروحانية
          </p>
        </div>

        <!-- بطاقات الخدمات -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- قراءة القرآن -->
          <div class="glass-card group" data-aos="fade-up" data-aos-delay="100">
            <div class="text-center">
              <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M21,5C19.89,4.65 18.67,4.5 17.5,4.5C15.55,4.5 13.45,4.9 12,6C10.55,4.9 8.45,4.5 6.5,4.5C4.55,4.5 2.45,4.9 1,6V20.65C1,20.9 1.25,21.15 1.5,21.15C1.6,21.15 1.65,21.1 1.75,21.1C3.1,20.45 5.05,20 6.5,20C8.45,20 10.55,20.4 12,21.5C13.35,20.65 15.8,20 17.5,20C19.15,20 20.85,20.3 22.25,21.05C22.35,21.1 22.4,21.1 22.5,21.1C22.75,21.1 23,20.85 23,20.6V6C22.4,5.55 21.75,5.25 21,5M21,18.5C19.9,18.15 18.7,18 17.5,18C15.8,18 13.35,18.65 12,19.5V8C13.35,7.15 15.8,6.5 17.5,6.5C18.7,6.5 19.9,6.65 21,7V18.5Z"/>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-slate-800 dark:text-slate-200 mb-2">
                {{ $t('services.quran.title') }}
              </h3>
              <p class="text-slate-600 dark:text-slate-400 text-sm">
                {{ $t('services.quran.description') }}
              </p>
            </div>
          </div>

          <!-- اتجاه القبلة -->
          <div class="glass-card group" data-aos="fade-up" data-aos-delay="200">
            <div class="text-center">
              <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8M12,10A2,2 0 0,0 10,12A2,2 0 0,0 12,14A2,2 0 0,0 14,12A2,2 0 0,0 12,10M10,22C9.75,22 9.54,21.82 9.5,21.58L9.13,18.93C8.5,18.68 7.96,18.34 7.44,17.94L4.95,18.95C4.73,19.03 4.46,18.95 4.34,18.73L2.34,15.27C2.21,15.05 2.27,14.78 2.46,14.63L4.57,12.97L4.5,12L4.57,11L2.46,9.37C2.27,9.22 2.21,8.95 2.34,8.73L4.34,5.27C4.46,5.05 4.73,4.96 4.95,5.05L7.44,6.05C7.96,5.66 8.5,5.32 9.13,5.07L9.5,2.42C9.54,2.18 9.75,2 10,2H14C14.25,2 14.46,2.18 14.5,2.42L14.87,5.07C15.5,5.32 16.04,5.66 16.56,6.05L19.05,5.05C19.27,4.96 19.54,5.05 19.66,5.27L21.66,8.73C21.79,8.95 21.73,9.22 21.54,9.37L19.43,11L19.5,12L19.43,13L21.54,14.63C21.73,14.78 21.79,15.05 21.66,15.27L19.66,18.73C19.54,18.95 19.27,19.04 19.05,18.95L16.56,17.95C16.04,18.34 15.5,18.68 14.87,18.93L14.5,21.58C14.46,21.82 14.25,22 14,22H10M11.25,4L10.88,6.61C9.68,6.86 8.62,7.5 7.85,8.39L5.44,7.35L4.69,8.65L6.8,10.2C6.4,11.37 6.4,12.64 6.8,13.8L4.68,15.36L5.43,16.66L7.86,15.62C8.63,16.5 9.68,17.14 10.87,17.38L11.24,20H12.76L13.13,17.39C14.32,17.14 15.37,16.5 16.14,15.62L18.57,16.66L19.32,15.36L17.2,13.81C17.6,12.64 17.6,11.37 17.2,10.2L19.31,8.65L18.56,7.35L16.15,8.39C15.38,7.5 14.32,6.86 13.12,6.62L12.75,4H11.25Z"/>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-slate-800 dark:text-slate-200 mb-2">
                {{ $t('services.qibla.title') }}
              </h3>
              <p class="text-slate-600 dark:text-slate-400 text-sm">
                {{ $t('services.qibla.description') }}
              </p>
            </div>
          </div>

          <!-- المسبحة الإلكترونية -->
          <div class="glass-card group" data-aos="fade-up" data-aos-delay="300">
            <div class="text-center">
              <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-slate-800 dark:text-slate-200 mb-2">
                {{ $t('services.tasbih.title') }}
              </h3>
              <p class="text-slate-600 dark:text-slate-400 text-sm">
                {{ $t('services.tasbih.description') }}
              </p>
            </div>
          </div>
        </div>

        <!-- رابط لجميع الخدمات -->
        <div class="text-center mt-12" data-aos="fade-up" data-aos-delay="400">
          <NuxtLink to="/services" class="btn-primary">
            عرض جميع الخدمات
          </NuxtLink>
        </div>
      </div>
    </section>

    <!-- قسم الدعاء الختامي -->
    <section class="py-16">
      <div class="container mx-auto text-center">
        <div class="glass-card max-w-2xl mx-auto" data-aos="fade-up">
          <p class="text-xl font-arabic text-slate-700 dark:text-slate-300 mb-4">
            {{ $t('home.prayer') }}
          </p>
          <div class="w-16 h-1 bg-gradient-to-r from-gold-500 to-gold-600 mx-auto rounded-full"></div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

// إعدادات SEO للصفحة الرئيسية
useHead({
  title: 'الذِّكر الحكيم - الصفحة الرئيسية',
  meta: [
    { name: 'description', content: 'موقع إسلامي تعليمي وتفاعلي يعرض القرآن الكريم كاملاً مع خدمات دينية متنوعة' }
  ]
})

// تهيئة مكتبة AOS للأنيميشن
onMounted(() => {
  if (process.client) {
    import('aos').then((AOS) => {
      AOS.default.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true,
        offset: 100
      })
    })
  }
})
</script>

<style scoped>
/* أنيميشن مخصص للبطاقات */
.stat-card:hover .stat-number {
  @apply animate-pulse;
}

.glass-card:hover {
  @apply shadow-2xl;
  transform: translateY(-5px) scale(1.02);
}

/* تأثير الخلفية الإسلامية */
.islamic-pattern {
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(30, 58, 138, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
}
</style>
