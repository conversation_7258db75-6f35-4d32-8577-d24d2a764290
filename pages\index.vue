<template>
  <div class="min-h-screen">
    <!-- قسم الترحيب الرئيسي -->
    <section class="py-20">
      <div class="container mx-auto px-4 text-center">
        <!-- العنوان الرئيسي -->
        <div class="mb-12">
          <h1 class="text-4xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent font-arabic">
            مرحبًا بك في الذِّكر الحكيم
          </h1>
          <p class="text-xl md:text-2xl text-slate-600 dark:text-slate-400 mb-6">
            دليلك نحو النور والهداية
          </p>
          <p class="text-lg text-slate-600 dark:text-slate-400 max-w-3xl mx-auto leading-relaxed">
            موقع إسلامي تعليمي وتفاعلي يقدم القرآن الكريم كاملاً مع خدمات دينية متنوعة لتعزيز رحلتك الروحانية
          </p>
        </div>

        <!-- زر استكشاف الخدمات -->
        <div class="mb-16">
          <NuxtLink to="/services" class="inline-flex items-center gap-2 bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
            <span>استكشف خدماتنا</span>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
            </svg>
          </NuxtLink>
        </div>
      </div>
    </section>

    <!-- قسم إحصائيات القرآن الكريم -->
    <section class="py-16 bg-white/50 dark:bg-slate-800/50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-slate-800 dark:text-slate-200 font-arabic mb-4">
            القرآن الكريم في أرقام
          </h2>
          <p class="text-lg text-slate-600 dark:text-slate-400">
            إحصائيات مباركة من كتاب الله العزيز
          </p>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- عدد السور -->
          <div class="bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border border-white/30 dark:border-slate-700/30 rounded-2xl p-6 text-center shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
            <div class="text-3xl font-bold bg-gradient-to-r from-amber-500 to-amber-600 bg-clip-text text-transparent mb-2">114</div>
            <div class="text-sm text-slate-600 dark:text-slate-400">سورة مباركة، خيرُ كلام الله</div>
            <div class="mt-4">
              <svg class="w-12 h-12 mx-auto text-amber-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"/>
              </svg>
            </div>
          </div>

          <!-- عدد الآيات -->
          <div class="bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border border-white/30 dark:border-slate-700/30 rounded-2xl p-6 text-center shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
            <div class="text-3xl font-bold bg-gradient-to-r from-blue-500 to-blue-600 bg-clip-text text-transparent mb-2">6236</div>
            <div class="text-sm text-slate-600 dark:text-slate-400">آية تهدي وتُبصر</div>
            <div class="mt-4">
              <svg class="w-12 h-12 mx-auto text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
              </svg>
            </div>
          </div>

          <!-- عدد الأجزاء -->
          <div class="bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border border-white/30 dark:border-slate-700/30 rounded-2xl p-6 text-center shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
            <div class="text-3xl font-bold bg-gradient-to-r from-green-500 to-green-600 bg-clip-text text-transparent mb-2">30</div>
            <div class="text-sm text-slate-600 dark:text-slate-400">جزءًا لتيسير التلاوة والقراءة</div>
            <div class="mt-4">
              <svg class="w-12 h-12 mx-auto text-green-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19Z"/>
              </svg>
            </div>
          </div>

          <!-- عدد الصفحات -->
          <div class="bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border border-white/30 dark:border-slate-700/30 rounded-2xl p-6 text-center shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
            <div class="text-3xl font-bold bg-gradient-to-r from-purple-500 to-purple-600 bg-clip-text text-transparent mb-2">604</div>
            <div class="text-sm text-slate-600 dark:text-slate-400">صفحات تحوي نوراً ورحمة</div>
            <div class="mt-4">
              <svg class="w-12 h-12 mx-auto text-purple-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M16,9H8V7H16M16,13H8V11H16M16,17H8V15H16M6,2A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2H6Z"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- قسم الدعاء الختامي -->
    <section class="py-16">
      <div class="container mx-auto px-4 text-center">
        <div class="bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border border-white/30 dark:border-slate-700/30 rounded-2xl p-8 max-w-2xl mx-auto shadow-xl">
          <p class="text-xl font-arabic text-slate-700 dark:text-slate-300 mb-4">
            ونسأل الله أن يجعل هذا العمل خالصًا لوجهه الكريم
          </p>
          <div class="w-16 h-1 bg-gradient-to-r from-amber-500 to-amber-600 mx-auto rounded-full"></div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
// إعدادات SEO للصفحة الرئيسية
useHead({
  title: 'الذِّكر الحكيم - الصفحة الرئيسية',
  meta: [
    { name: 'description', content: 'موقع إسلامي تعليمي وتفاعلي يعرض القرآن الكريم كاملاً مع خدمات دينية متنوعة' }
  ]
})
</script>
