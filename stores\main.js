import { defineStore } from 'pinia'

export const useMainStore = defineStore('main', {
  state: () => ({
    // إعدادات المظهر
    isDarkMode: false,
    
    // إعدادات اللغة
    currentLanguage: 'ar',
    
    // إعدادات المستخدم
    userPreferences: {
      fontSize: 'medium',
      autoPlay: false,
      notifications: true,
      soundEnabled: true
    },
    
    // بيانات القرآن الكريم
    quranData: {
      totalSuras: 114,
      totalVerses: 6236,
      totalParts: 30,
      totalPages: 604
    },
    
    // حالة التطبيق
    isLoading: false,
    currentPage: 'home',
    
    // إعدادات الخدمات
    services: [
      {
        id: 'quran',
        name: 'قراءة القرآن الكريم',
        nameEn: 'Read the Holy Quran',
        icon: 'book',
        color: 'green',
        enabled: true,
        description: 'اقرأ القرآن الكريم كاملاً مع التفسير والترجمة'
      },
      {
        id: 'qibla',
        name: 'اتجاه القبلة',
        nameEn: 'Qibla Direction',
        icon: 'compass',
        color: 'blue',
        enabled: true,
        description: 'حدد اتجاه القبلة الشريفة من أي مكان في العالم'
      },
      {
        id: 'tasbih',
        name: 'مسبحة إلكترونية',
        nameEn: 'Electronic Tasbih',
        icon: 'circle',
        color: 'purple',
        enabled: true,
        description: 'سبح الله واذكره بالمسبحة الإلكترونية التفاعلية'
      },
      {
        id: 'calendar',
        name: 'التقويم الهجري',
        nameEn: 'Hijri Calendar',
        icon: 'calendar',
        color: 'orange',
        enabled: true,
        description: 'تابع التواريخ الهجرية والمناسبات الإسلامية'
      },
      {
        id: 'azkar',
        name: 'الأذكار اليومية',
        nameEn: 'Daily Azkar',
        icon: 'clock',
        color: 'teal',
        enabled: true,
        description: 'أذكار الصباح والمساء وأذكار متنوعة'
      },
      {
        id: 'hadith',
        name: 'الأحاديث النبوية',
        nameEn: 'Prophetic Hadiths',
        icon: 'document',
        color: 'indigo',
        enabled: true,
        description: 'مجموعة من الأحاديث النبوية الشريفة'
      },
      {
        id: 'stories',
        name: 'قصص القرآن',
        nameEn: 'Quranic Stories',
        icon: 'book-open',
        color: 'pink',
        enabled: true,
        description: 'قصص الأنبياء والصالحين من القرآن الكريم'
      }
    ]
  }),

  getters: {
    // الحصول على الخدمات المفعلة
    enabledServices: (state) => {
      return state.services.filter(service => service.enabled)
    },
    
    // الحصول على خدمة معينة
    getServiceById: (state) => {
      return (id) => state.services.find(service => service.id === id)
    },
    
    // التحقق من حالة الوضع الليلي
    getThemeMode: (state) => {
      return state.isDarkMode ? 'dark' : 'light'
    },
    
    // الحصول على إعدادات المستخدم
    getUserPreferences: (state) => {
      return state.userPreferences
    },
    
    // الحصول على بيانات القرآن
    getQuranStats: (state) => {
      return state.quranData
    }
  },

  actions: {
    // تبديل الوضع الليلي/النهاري
    toggleDarkMode() {
      this.isDarkMode = !this.isDarkMode
      this.saveToLocalStorage()
      
      // تطبيق التغيير على DOM
      if (process.client) {
        document.documentElement.classList.toggle('dark', this.isDarkMode)
      }
    },
    
    // تغيير اللغة
    setLanguage(language) {
      this.currentLanguage = language
      this.saveToLocalStorage()
      
      // تحديث اتجاه الصفحة
      if (process.client) {
        document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr'
        document.documentElement.lang = language
      }
    },
    
    // تحديث إعدادات المستخدم
    updateUserPreferences(preferences) {
      this.userPreferences = { ...this.userPreferences, ...preferences }
      this.saveToLocalStorage()
    },
    
    // تعيين حالة التحميل
    setLoading(loading) {
      this.isLoading = loading
    },
    
    // تعيين الصفحة الحالية
    setCurrentPage(page) {
      this.currentPage = page
    },
    
    // تفعيل/إلغاء تفعيل خدمة
    toggleService(serviceId) {
      const service = this.services.find(s => s.id === serviceId)
      if (service) {
        service.enabled = !service.enabled
        this.saveToLocalStorage()
      }
    },
    
    // حفظ البيانات في التخزين المحلي
    saveToLocalStorage() {
      if (process.client) {
        const data = {
          isDarkMode: this.isDarkMode,
          currentLanguage: this.currentLanguage,
          userPreferences: this.userPreferences,
          services: this.services
        }
        localStorage.setItem('alzekr-alhakem-settings', JSON.stringify(data))
      }
    },
    
    // تحميل البيانات من التخزين المحلي
    loadFromLocalStorage() {
      if (process.client) {
        try {
          const saved = localStorage.getItem('alzekr-alhakem-settings')
          if (saved) {
            const data = JSON.parse(saved)
            this.isDarkMode = data.isDarkMode || false
            this.currentLanguage = data.currentLanguage || 'ar'
            this.userPreferences = { ...this.userPreferences, ...data.userPreferences }
            
            if (data.services) {
              this.services = data.services
            }
            
            // تطبيق الإعدادات على DOM
            document.documentElement.classList.toggle('dark', this.isDarkMode)
            document.documentElement.dir = this.currentLanguage === 'ar' ? 'rtl' : 'ltr'
            document.documentElement.lang = this.currentLanguage
          }
        } catch (error) {
          console.error('Error loading settings from localStorage:', error)
        }
      }
    },
    
    // إعادة تعيين الإعدادات للافتراضية
    resetSettings() {
      this.isDarkMode = false
      this.currentLanguage = 'ar'
      this.userPreferences = {
        fontSize: 'medium',
        autoPlay: false,
        notifications: true,
        soundEnabled: true
      }
      
      // إعادة تفعيل جميع الخدمات
      this.services.forEach(service => {
        service.enabled = true
      })
      
      this.saveToLocalStorage()
      
      // تطبيق التغييرات على DOM
      if (process.client) {
        document.documentElement.classList.remove('dark')
        document.documentElement.dir = 'rtl'
        document.documentElement.lang = 'ar'
      }
    },
    
    // تهيئة المتجر
    initialize() {
      this.loadFromLocalStorage()
    }
  }
})
