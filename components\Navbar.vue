<template>
  <nav class="navbar">
    <div class="container mx-auto flex items-center justify-between">
      <!-- الشعار واسم الموقع -->
      <div class="flex items-center space-x-4 rtl:space-x-reverse">
        <div class="w-12 h-12 bg-gradient-to-br from-gold-500 to-gold-600 rounded-full flex items-center justify-center">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"/>
          </svg>
        </div>
        <div class="hidden md:block">
          <h1 class="text-xl font-bold text-gold-gradient font-arabic">{{ $t('site.name') }}</h1>
          <p class="text-sm text-slate-600 dark:text-slate-400">{{ $t('site.tagline') }}</p>
        </div>
      </div>

      <!-- روابط التنقل -->
      <div class="hidden lg:flex items-center space-x-8 rtl:space-x-reverse">
        <NuxtLink 
          to="/" 
          class="nav-link"
          :class="{ 'active': $route.path === '/' }"
        >
          {{ $t('nav.home') }}
        </NuxtLink>
        <NuxtLink 
          to="/services" 
          class="nav-link"
          :class="{ 'active': $route.path === '/services' }"
        >
          {{ $t('nav.services') }}
        </NuxtLink>
        <NuxtLink 
          to="/about" 
          class="nav-link"
          :class="{ 'active': $route.path === '/about' }"
        >
          {{ $t('nav.about') }}
        </NuxtLink>
      </div>

      <!-- أزرار التحكم -->
      <div class="flex items-center space-x-4 rtl:space-x-reverse">
        <!-- تبديل اللغة -->
        <button 
          @click="toggleLanguage"
          class="p-2 rounded-lg glass hover:bg-white/30 dark:hover:bg-slate-800/30 transition-colors"
          :title="$t('nav.language')"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"/>
          </svg>
        </button>

        <!-- تبديل الوضع الليلي/النهاري -->
        <button 
          @click="toggleDarkMode"
          class="p-2 rounded-lg glass hover:bg-white/30 dark:hover:bg-slate-800/30 transition-colors"
          :title="isDark ? $t('nav.lightMode') : $t('nav.darkMode')"
        >
          <svg v-if="isDark" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
          </svg>
          <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
          </svg>
        </button>

        <!-- قائمة الهاتف المحمول -->
        <button 
          @click="toggleMobileMenu"
          class="lg:hidden p-2 rounded-lg glass hover:bg-white/30 dark:hover:bg-slate-800/30 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- قائمة الهاتف المحمول -->
    <div 
      v-show="showMobileMenu" 
      class="lg:hidden mt-4 p-4 glass rounded-xl"
      @click="showMobileMenu = false"
    >
      <div class="flex flex-col space-y-4">
        <NuxtLink 
          to="/" 
          class="nav-link-mobile"
          :class="{ 'active': $route.path === '/' }"
        >
          {{ $t('nav.home') }}
        </NuxtLink>
        <NuxtLink 
          to="/services" 
          class="nav-link-mobile"
          :class="{ 'active': $route.path === '/services' }"
        >
          {{ $t('nav.services') }}
        </NuxtLink>
        <NuxtLink 
          to="/about" 
          class="nav-link-mobile"
          :class="{ 'active': $route.path === '/about' }"
        >
          {{ $t('nav.about') }}
        </NuxtLink>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'

const { locale, setLocale } = useI18n()
const showMobileMenu = ref(false)
const isDark = ref(false)

const toggleLanguage = () => {
  const newLocale = locale.value === 'ar' ? 'en' : 'ar'
  setLocale(newLocale)
  
  // تحديث اتجاه الصفحة
  document.documentElement.dir = newLocale === 'ar' ? 'rtl' : 'ltr'
}

const toggleDarkMode = () => {
  isDark.value = !isDark.value
  if (process.client) {
    document.documentElement.classList.toggle('dark', isDark.value)
  }
}

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

onMounted(() => {
  // تعيين اتجاه الصفحة عند التحميل
  document.documentElement.dir = locale.value === 'ar' ? 'rtl' : 'ltr'
})
</script>

<style scoped>
.nav-link {
  @apply text-slate-700 dark:text-slate-300;
  @apply hover:text-gold-600 dark:hover:text-gold-400;
  @apply transition-colors duration-300;
  @apply font-medium;
  @apply relative;
}

.nav-link.active {
  @apply text-gold-600 dark:text-gold-400;
}

.nav-link::after {
  content: '';
  @apply absolute bottom-0 left-0 w-0 h-0.5;
  @apply bg-gradient-to-r from-gold-500 to-gold-600;
  @apply transition-all duration-300;
}

.nav-link:hover::after,
.nav-link.active::after {
  @apply w-full;
}

.nav-link-mobile {
  @apply text-slate-700 dark:text-slate-300;
  @apply hover:text-gold-600 dark:hover:text-gold-400;
  @apply transition-colors duration-300;
  @apply font-medium;
  @apply py-2 px-4 rounded-lg;
  @apply hover:bg-white/20 dark:hover:bg-slate-800/20;
}

.nav-link-mobile.active {
  @apply text-gold-600 dark:text-gold-400;
  @apply bg-white/20 dark:bg-slate-800/20;
}
</style>
