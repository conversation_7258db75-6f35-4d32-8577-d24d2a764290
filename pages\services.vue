<template>
  <div class="min-h-screen py-8">
    <!-- عنوان الصفحة -->
    <section class="text-center mb-16">
      <div class="container mx-auto" data-aos="fade-up">
        <h1 class="text-4xl md:text-5xl font-bold text-gradient font-arabic mb-4">
          {{ $t('services.title') }}
        </h1>
        <p class="text-xl text-slate-600 dark:text-slate-400 max-w-3xl mx-auto">
          {{ $t('services.subtitle') }}
        </p>
      </div>
    </section>

    <!-- شبكة الخدمات -->
    <section class="container mx-auto">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        
        <!-- خدمة قراءة القرآن الكريم -->
        <div class="service-card" data-aos="fade-up" data-aos-delay="100">
          <div class="service-icon bg-gradient-to-br from-green-500 to-green-600">
            <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M21,5C19.89,4.65 18.67,4.5 17.5,4.5C15.55,4.5 13.45,4.9 12,6C10.55,4.9 8.45,4.5 6.5,4.5C4.55,4.5 2.45,4.9 1,6V20.65C1,20.9 1.25,21.15 1.5,21.15C1.6,21.15 1.65,21.1 1.75,21.1C3.1,20.45 5.05,20 6.5,20C8.45,20 10.55,20.4 12,21.5C13.35,20.65 15.8,20 17.5,20C19.15,20 20.85,20.3 22.25,21.05C22.35,21.1 22.4,21.1 22.5,21.1C22.75,21.1 23,20.85 23,20.6V6C22.4,5.55 21.75,5.25 21,5M21,18.5C19.9,18.15 18.7,18 17.5,18C15.8,18 13.35,18.65 12,19.5V8C13.35,7.15 15.8,6.5 17.5,6.5C18.7,6.5 19.9,6.65 21,7V18.5Z"/>
            </svg>
          </div>
          <h3 class="service-title">{{ $t('services.quran.title') }}</h3>
          <p class="service-description">{{ $t('services.quran.description') }}</p>
          <button class="service-button">
            <span>ابدأ القراءة</span>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
            </svg>
          </button>
        </div>

        <!-- خدمة اتجاه القبلة -->
        <div class="service-card" data-aos="fade-up" data-aos-delay="200">
          <div class="service-icon bg-gradient-to-br from-blue-500 to-blue-600">
            <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8M12,10A2,2 0 0,0 10,12A2,2 0 0,0 12,14A2,2 0 0,0 14,12A2,2 0 0,0 12,10M10,22C9.75,22 9.54,21.82 9.5,21.58L9.13,18.93C8.5,18.68 7.96,18.34 7.44,17.94L4.95,18.95C4.73,19.03 4.46,18.95 4.34,18.73L2.34,15.27C2.21,15.05 2.27,14.78 2.46,14.63L4.57,12.97L4.5,12L4.57,11L2.46,9.37C2.27,9.22 2.21,8.95 2.34,8.73L4.34,5.27C4.46,5.05 4.73,4.96 4.95,5.05L7.44,6.05C7.96,5.66 8.5,5.32 9.13,5.07L9.5,2.42C9.54,2.18 9.75,2 10,2H14C14.25,2 14.46,2.18 14.5,2.42L14.87,5.07C15.5,5.32 16.04,5.66 16.56,6.05L19.05,5.05C19.27,4.96 19.54,5.05 19.66,5.27L21.66,8.73C21.79,8.95 21.73,9.22 21.54,9.37L19.43,11L19.5,12L19.43,13L21.54,14.63C21.73,14.78 21.79,15.05 21.66,15.27L19.66,18.73C19.54,18.95 19.27,19.04 19.05,18.95L16.56,17.95C16.04,18.34 15.5,18.68 14.87,18.93L14.5,21.58C14.46,21.82 14.25,22 14,22H10M11.25,4L10.88,6.61C9.68,6.86 8.62,7.5 7.85,8.39L5.44,7.35L4.69,8.65L6.8,10.2C6.4,11.37 6.4,12.64 6.8,13.8L4.68,15.36L5.43,16.66L7.86,15.62C8.63,16.5 9.68,17.14 10.87,17.38L11.24,20H12.76L13.13,17.39C14.32,17.14 15.37,16.5 16.14,15.62L18.57,16.66L19.32,15.36L17.2,13.81C17.6,12.64 17.6,11.37 17.2,10.2L19.31,8.65L18.56,7.35L16.15,8.39C15.38,7.5 14.32,6.86 13.12,6.62L12.75,4H11.25Z"/>
            </svg>
          </div>
          <h3 class="service-title">{{ $t('services.qibla.title') }}</h3>
          <p class="service-description">{{ $t('services.qibla.description') }}</p>
          <button class="service-button">
            <span>تحديد القبلة</span>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
            </svg>
          </button>
        </div>

        <!-- خدمة المسبحة الإلكترونية -->
        <div class="service-card" data-aos="fade-up" data-aos-delay="300">
          <div class="service-icon bg-gradient-to-br from-purple-500 to-purple-600">
            <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
            </svg>
          </div>
          <h3 class="service-title">{{ $t('services.tasbih.title') }}</h3>
          <p class="service-description">{{ $t('services.tasbih.description') }}</p>
          <button class="service-button">
            <span>ابدأ التسبيح</span>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
            </svg>
          </button>
        </div>

        <!-- خدمة التقويم الهجري -->
        <div class="service-card" data-aos="fade-up" data-aos-delay="400">
          <div class="service-icon bg-gradient-to-br from-orange-500 to-orange-600">
            <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M19,3H18V1H16V3H8V1H6V3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V8H19V19M5,6V5H19V6H5Z"/>
            </svg>
          </div>
          <h3 class="service-title">{{ $t('services.calendar.title') }}</h3>
          <p class="service-description">{{ $t('services.calendar.description') }}</p>
          <button class="service-button">
            <span>عرض التقويم</span>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
            </svg>
          </button>
        </div>

        <!-- خدمة الأذكار اليومية -->
        <div class="service-card" data-aos="fade-up" data-aos-delay="500">
          <div class="service-icon bg-gradient-to-br from-teal-500 to-teal-600">
            <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.9L16.2,16.2Z"/>
            </svg>
          </div>
          <h3 class="service-title">{{ $t('services.azkar.title') }}</h3>
          <p class="service-description">{{ $t('services.azkar.description') }}</p>
          <button class="service-button">
            <span>تصفح الأذكار</span>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
            </svg>
          </button>
        </div>

        <!-- خدمة الأحاديث النبوية -->
        <div class="service-card" data-aos="fade-up" data-aos-delay="600">
          <div class="service-icon bg-gradient-to-br from-indigo-500 to-indigo-600">
            <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
            </svg>
          </div>
          <h3 class="service-title">{{ $t('services.hadith.title') }}</h3>
          <p class="service-description">{{ $t('services.hadith.description') }}</p>
          <button class="service-button">
            <span>قراءة الأحاديث</span>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
            </svg>
          </button>
        </div>

        <!-- خدمة قصص القرآن -->
        <div class="service-card" data-aos="fade-up" data-aos-delay="700">
          <div class="service-icon bg-gradient-to-br from-pink-500 to-pink-600">
            <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M18,2A2,2 0 0,1 20,4V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V4A2,2 0 0,1 6,2H18M18,4H6V20H18V4M7,9H17V7H7V9M7,11H17V13H7V11M7,15H13V17H7V15Z"/>
            </svg>
          </div>
          <h3 class="service-title">{{ $t('services.stories.title') }}</h3>
          <p class="service-description">{{ $t('services.stories.description') }}</p>
          <button class="service-button">
            <span>اقرأ القصص</span>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
            </svg>
          </button>
        </div>

      </div>
    </section>

    <!-- قسم دعوة للعمل -->
    <section class="py-16 mt-16">
      <div class="container mx-auto text-center">
        <div class="glass-card max-w-2xl mx-auto" data-aos="fade-up">
          <h2 class="text-2xl font-bold text-slate-800 dark:text-slate-200 font-arabic mb-4">
            ابدأ رحلتك الروحانية اليوم
          </h2>
          <p class="text-slate-600 dark:text-slate-400 mb-6">
            استفد من جميع خدماتنا الإسلامية المتطورة لتعزيز علاقتك بالله وتطوير معرفتك الدينية
          </p>
          <NuxtLink to="/" class="btn-gold">
            العودة للرئيسية
          </NuxtLink>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

// إعدادات SEO لصفحة الخدمات
useHead({
  title: 'الذِّكر الحكيم - خدماتنا الإسلامية',
  meta: [
    { name: 'description', content: 'مجموعة شاملة من الخدمات الإسلامية: قراءة القرآن، اتجاه القبلة، المسبحة الإلكترونية، التقويم الهجري، الأذكار، الأحاديث، وقصص القرآن' }
  ]
})

// تهيئة مكتبة AOS للأنيميشن
onMounted(() => {
  if (process.client) {
    import('aos').then((AOS) => {
      AOS.default.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true,
        offset: 100
      })
    })
  }
})
</script>

<style scoped>
.service-card {
  @apply glass-card;
  @apply text-center;
  @apply group;
  @apply cursor-pointer;
  @apply transition-all duration-300;
}

.service-card:hover {
  @apply shadow-2xl;
  transform: translateY(-8px) scale(1.02);
}

.service-icon {
  @apply w-20 h-20;
  @apply rounded-full;
  @apply flex items-center justify-center;
  @apply mx-auto mb-6;
  @apply group-hover:scale-110;
  @apply transition-transform duration-300;
  @apply shadow-lg;
}

.service-title {
  @apply text-xl font-semibold;
  @apply text-slate-800 dark:text-slate-200;
  @apply mb-3;
  @apply font-arabic;
}

.service-description {
  @apply text-slate-600 dark:text-slate-400;
  @apply mb-6;
  @apply leading-relaxed;
}

.service-button {
  @apply bg-gradient-to-r from-gold-500 to-gold-600;
  @apply hover:from-gold-600 hover:to-gold-700;
  @apply text-white font-medium;
  @apply px-6 py-3 rounded-lg;
  @apply transition-all duration-300;
  @apply flex items-center justify-center space-x-2 rtl:space-x-reverse;
  @apply mx-auto;
  @apply group-hover:shadow-lg;
  @apply transform group-hover:scale-105;
}
</style>
