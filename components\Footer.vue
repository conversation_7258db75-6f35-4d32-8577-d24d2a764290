<template>
  <footer class="footer mt-16">
    <div class="container mx-auto">
      <!-- المحتوى الرئيسي للتذييل -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
        <!-- معلومات الموقع -->
        <div class="text-center md:text-right">
          <div class="flex items-center justify-center md:justify-start space-x-3 rtl:space-x-reverse mb-4">
            <div class="w-10 h-10 bg-gradient-to-br from-gold-500 to-gold-600 rounded-full flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"/>
              </svg>
            </div>
            <h3 class="text-lg font-bold text-gold-gradient font-arabic">{{ $t('site.name') }}</h3>
          </div>
          <p class="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
            {{ $t('site.description') }}
          </p>
        </div>

        <!-- روابط سريعة -->
        <div class="text-center">
          <h4 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-4">
            روابط سريعة
          </h4>
          <div class="flex flex-col space-y-2">
            <NuxtLink 
              to="/" 
              class="footer-link"
            >
              {{ $t('nav.home') }}
            </NuxtLink>
            <NuxtLink 
              to="/services" 
              class="footer-link"
            >
              {{ $t('nav.services') }}
            </NuxtLink>
            <NuxtLink 
              to="/about" 
              class="footer-link"
            >
              {{ $t('nav.about') }}
            </NuxtLink>
            <a href="#" class="footer-link">
              {{ $t('footer.links.privacy') }}
            </a>
            <a href="#" class="footer-link">
              {{ $t('footer.links.terms') }}
            </a>
          </div>
        </div>

        <!-- معلومات المطور -->
        <div class="text-center md:text-left">
          <h4 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-4">
            {{ $t('about.developer.title') }}
          </h4>
          <p class="text-sm text-slate-600 dark:text-slate-400 mb-2">
            {{ $t('about.developer.name') }}
          </p>
          <p class="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
            {{ $t('about.developer.thanks') }}
          </p>
          
          <!-- روابط التواصل الاجتماعي -->
          <div class="flex justify-center md:justify-start space-x-4 rtl:space-x-reverse mt-4">
            <a href="#" class="social-link" title="GitHub">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
            </a>
            <a href="#" class="social-link" title="LinkedIn">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
            </a>
            <a href="#" class="social-link" title="Twitter">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
              </svg>
            </a>
          </div>
        </div>
      </div>

      <!-- خط فاصل -->
      <div class="border-t border-white/20 dark:border-slate-700/30 pt-6">
        <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <!-- حقوق النشر -->
          <div class="text-center md:text-right">
            <p class="text-sm text-slate-600 dark:text-slate-400">
              {{ $t('footer.copyright') }}
            </p>
          </div>

          <!-- دعاء ختامي -->
          <div class="text-center">
            <p class="text-sm text-slate-600 dark:text-slate-400 font-arabic">
              {{ $t('footer.blessing') }}
            </p>
          </div>
        </div>
      </div>

      <!-- آية قرآنية -->
      <div class="mt-6 text-center">
        <div class="glass-card max-w-2xl mx-auto">
          <p class="verse-text font-arabic text-lg mb-2">
            ﴿ وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا * وَيَرْزُقْهُ مِنْ حَيْثُ لَا يَحْتَسِبُ ﴾
          </p>
          <p class="text-sm text-slate-500 dark:text-slate-400">
            سورة الطلاق - الآيات 2-3
          </p>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
// لا حاجة لمنطق إضافي في Footer
</script>

<style scoped>
.footer-link {
  @apply text-slate-600 dark:text-slate-400;
  @apply hover:text-gold-600 dark:hover:text-gold-400;
  @apply transition-colors duration-300;
  @apply text-sm;
}

.social-link {
  @apply w-10 h-10;
  @apply bg-white/10 dark:bg-slate-800/20;
  @apply hover:bg-gold-500 dark:hover:bg-gold-600;
  @apply text-slate-600 dark:text-slate-400;
  @apply hover:text-white;
  @apply rounded-full;
  @apply flex items-center justify-center;
  @apply transition-all duration-300;
  @apply transform hover:scale-110;
}

.verse-text {
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.1) 0%, 
    rgba(30, 58, 138, 0.1) 100%);
  @apply rounded-lg p-4;
}
</style>
