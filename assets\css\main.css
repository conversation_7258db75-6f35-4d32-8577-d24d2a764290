@tailwind base;
@tailwind components;
@tailwind utilities;

/* خطوط عربية */
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap');

/* إعدادات أساسية */
@layer base {
  html {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
  }
  
  body {
    @apply bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800;
    @apply text-slate-800 dark:text-slate-200;
    @apply transition-colors duration-300;
  }
  
  .font-arabic {
    font-family: '<PERSON>i', serif;
  }
  
  .font-cairo {
    font-family: 'Cairo', sans-serif;
  }
}

/* مكونات مخصصة */
@layer components {
  /* تأثير Glassmorphism */
  .glass {
    @apply bg-white/20 dark:bg-slate-800/20;
    @apply backdrop-blur-md;
    @apply border border-white/30 dark:border-slate-700/30;
    @apply shadow-xl;
    @apply rounded-2xl;
  }
  
  .glass-card {
    @apply glass;
    @apply p-6;
    @apply transition-all duration-300;
    @apply hover:bg-white/30 dark:hover:bg-slate-800/30;
    @apply hover:shadow-2xl;
    @apply hover:scale-105;
  }
  
  /* أزرار مخصصة */
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-blue-700;
    @apply hover:from-blue-700 hover:to-blue-800;
    @apply text-white font-semibold;
    @apply px-6 py-3 rounded-xl;
    @apply transition-all duration-300;
    @apply shadow-lg hover:shadow-xl;
    @apply transform hover:scale-105;
  }
  
  .btn-gold {
    @apply bg-gradient-to-r from-gold-500 to-gold-600;
    @apply hover:from-gold-600 hover:to-gold-700;
    @apply text-white font-semibold;
    @apply px-6 py-3 rounded-xl;
    @apply transition-all duration-300;
    @apply shadow-lg hover:shadow-xl;
    @apply transform hover:scale-105;
  }
  
  /* نصوص مخصصة */
  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-purple-600;
    @apply bg-clip-text text-transparent;
  }
  
  .text-gold-gradient {
    @apply bg-gradient-to-r from-gold-500 to-gold-600;
    @apply bg-clip-text text-transparent;
  }
  
  /* تخطيط الصفحة */
  .page-container {
    @apply min-h-screen;
    @apply flex flex-col;
  }
  
  .content-wrapper {
    @apply flex-1;
    @apply container mx-auto;
    @apply px-4 py-8;
  }
  
  /* شريط التنقل */
  .navbar {
    @apply glass;
    @apply fixed top-0 left-0 right-0 z-50;
    @apply px-4 py-3;
  }
  
  /* التذييل */
  .footer {
    @apply glass;
    @apply mt-auto;
    @apply px-4 py-6;
  }
  
  /* بطاقات الإحصائيات */
  .stat-card {
    @apply glass-card;
    @apply text-center;
  }
  
  .stat-number {
    @apply text-3xl font-bold;
    @apply text-gold-gradient;
    @apply group-hover:scale-110;
    @apply transition-transform duration-300;
  }
  
  .stat-label {
    @apply text-sm text-slate-600 dark:text-slate-400;
    @apply mt-2;
  }
}

/* أدوات مساعدة */
@layer utilities {
  .rtl {
    direction: rtl;
  }
  
  .ltr {
    direction: ltr;
  }
  
  /* تأثيرات الحركة */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }
  
  /* تأثيرات التمرير */
  .scroll-smooth {
    scroll-behavior: smooth;
  }
  
  /* إخفاء شريط التمرير */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

/* تأثيرات خاصة للموضوع الإسلامي */
.islamic-pattern {
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(30, 58, 138, 0.1) 0%, transparent 50%);
}

.verse-text {
  @apply font-arabic text-lg leading-relaxed;
  @apply text-slate-700 dark:text-slate-300;
  line-height: 2;
}

/* تأثيرات الأنيميشن المخصصة */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    transform: translateY(20px); 
    opacity: 0; 
  }
  to { 
    transform: translateY(0); 
    opacity: 1; 
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0% { box-shadow: 0 0 5px rgba(212, 175, 55, 0.5); }
  100% { box-shadow: 0 0 20px rgba(212, 175, 55, 0.8); }
}

/* تحسينات للطباعة */
@media print {
  .glass, .glass-card {
    @apply bg-white border border-gray-300;
    backdrop-filter: none;
  }
  
  .navbar, .footer {
    display: none;
  }
}
