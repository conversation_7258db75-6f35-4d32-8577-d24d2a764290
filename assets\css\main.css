@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* إعدادات أساسية */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-family: 'Cairo', sans-serif;
  direction: rtl;
}

body {
  background: linear-gradient(135deg, #f8fafc 0%, #e0f2fe 100%);
  color: #1e293b;
  min-height: 100vh;
}

.dark body {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e2e8f0;
}

.font-arabic {
  font-family: '<PERSON><PERSON>', serif;
}

.font-cairo {
  font-family: 'Cairo', sans-serif;
}

/* تأثير Glassmorphism */
.glass {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border-radius: 1rem;
}

.dark .glass {
  background: rgba(30, 41, 59, 0.2);
  border: 1px solid rgba(71, 85, 105, 0.3);
}

.glass-card {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
  transform: translateY(-2px) scale(1.02);
}

.dark .glass-card {
  background: rgba(30, 41, 59, 0.2);
  border: 1px solid rgba(71, 85, 105, 0.3);
}

.dark .glass-card:hover {
  background: rgba(30, 41, 59, 0.3);
}
  
/* أزرار مخصصة */
.btn-primary {
  background: linear-gradient(to right, #2563eb, #1d4ed8);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  display: inline-flex;
  align-items: center;
  text-decoration: none;
}

.btn-primary:hover {
  background: linear-gradient(to right, #1d4ed8, #1e40af);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px) scale(1.05);
}

.btn-gold {
  background: linear-gradient(to right, #f59e0b, #d97706);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  display: inline-flex;
  align-items: center;
  text-decoration: none;
}

.btn-gold:hover {
  background: linear-gradient(to right, #d97706, #b45309);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px) scale(1.05);
}
/* نصوص مخصصة */
.text-gradient {
  background: linear-gradient(to right, #2563eb, #9333ea);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.text-gold-gradient {
  background: linear-gradient(to right, #f59e0b, #d97706);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* تخطيط الصفحة */
.page-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

/* شريط التنقل */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  padding: 0.75rem 1rem;
}

/* التذييل */
.footer {
  margin-top: auto;
  padding: 1.5rem 1rem;
}

/* بطاقات الإحصائيات */
.stat-card {
  text-align: center;
}

.stat-number {
  font-size: 1.875rem;
  font-weight: 700;
  background: linear-gradient(to right, #f59e0b, #d97706);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  transition: transform 0.3s ease;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
  margin-top: 0.5rem;
}

.dark .stat-label {
  color: #94a3b8;
}

/* أدوات مساعدة */
.rtl {
  direction: rtl;
}

.ltr {
  direction: ltr;
}

/* تأثيرات خاصة للموضوع الإسلامي */
.islamic-pattern {
  background-image:
    radial-gradient(circle at 25% 25%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(30, 58, 138, 0.1) 0%, transparent 50%);
}

.verse-text {
  font-family: 'Amiri', serif;
  font-size: 1.125rem;
  line-height: 2;
  color: #334155;
}

.dark .verse-text {
  color: #cbd5e1;
}

/* تأثيرات الأنيميشن المخصصة */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
