import AOS from 'aos'
import 'aos/dist/aos.css'

export default defineNuxtPlugin(() => {
  // تهيئة مكتبة AOS للأنيميشن
  AOS.init({
    // مدة الأنيميشن بالميلي ثانية
    duration: 800,
    
    // نوع التسارع
    easing: 'ease-in-out',
    
    // تشغيل الأنيميشن مرة واحدة فقط
    once: true,
    
    // المسافة من أسفل الشاشة لبدء الأنيميشن
    offset: 100,
    
    // تأخير الأنيميشن
    delay: 0,
    
    // تعطيل الأنيميشن على الأجهزة المحمولة
    disable: false,
    
    // تحديث الأنيميشن عند تغيير حجم النافذة
    startEvent: 'DOMContentLoaded',
    
    // إعادة تهيئة الأنيميشن عند التنقل
    initClassName: 'aos-init',
    animatedClassName: 'aos-animate',
    useClassNames: false,
    disableMutationObserver: false,
    debounceDelay: 50,
    throttleDelay: 99,
  })

  // إعادة تحديث AOS عند التنقل بين الصفحات
  const router = useRouter()
  router.afterEach(() => {
    nextTick(() => {
      AOS.refresh()
    })
  })
})
