<template>
  <div class="min-h-screen py-8">
    <!-- عنوان الصفحة -->
    <section class="text-center mb-16">
      <div class="container mx-auto" data-aos="fade-up">
        <h1 class="text-4xl md:text-5xl font-bold text-gradient font-arabic mb-4">
          {{ $t('about.title') }}
        </h1>
        <p class="text-xl text-slate-600 dark:text-slate-400 max-w-3xl mx-auto">
          تعرف على رسالتنا ورؤيتنا في خدمة الإسلام والمسلمين
        </p>
      </div>
    </section>

    <!-- قسم الرسالة والرؤية -->
    <section class="container mx-auto mb-16">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        
        <!-- الرسالة -->
        <div class="glass-card" data-aos="fade-right">
          <div class="text-center mb-6">
            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,16.5L18,9.5L16.59,8.09L11,13.67L7.91,10.59L6.5,12L11,16.5Z"/>
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-slate-800 dark:text-slate-200 font-arabic">
              {{ $t('about.mission.title') }}
            </h2>
          </div>
          <p class="text-slate-600 dark:text-slate-400 leading-relaxed text-center">
            {{ $t('about.mission.content') }}
          </p>
        </div>

        <!-- الرؤية -->
        <div class="glass-card" data-aos="fade-left">
          <div class="text-center mb-6">
            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-slate-800 dark:text-slate-200 font-arabic">
              {{ $t('about.vision.title') }}
            </h2>
          </div>
          <p class="text-slate-600 dark:text-slate-400 leading-relaxed text-center">
            {{ $t('about.vision.content') }}
          </p>
        </div>
      </div>
    </section>

    <!-- قسم القيم -->
    <section class="container mx-auto mb-16">
      <div class="text-center mb-12" data-aos="fade-up">
        <h2 class="text-3xl font-bold text-slate-800 dark:text-slate-200 font-arabic mb-4">
          {{ $t('about.values.title') }}
        </h2>
        <p class="text-lg text-slate-600 dark:text-slate-400">
          القيم التي نؤمن بها ونسعى لتحقيقها في عملنا
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- الأصالة -->
        <div class="value-card" data-aos="fade-up" data-aos-delay="100">
          <div class="value-icon bg-gradient-to-br from-green-500 to-green-600">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M21,5C19.89,4.65 18.67,4.5 17.5,4.5C15.55,4.5 13.45,4.9 12,6C10.55,4.9 8.45,4.5 6.5,4.5C4.55,4.5 2.45,4.9 1,6V20.65C1,20.9 1.25,21.15 1.5,21.15C1.6,21.15 1.65,21.1 1.75,21.1C3.1,20.45 5.05,20 6.5,20C8.45,20 10.55,20.4 12,21.5C13.35,20.65 15.8,20 17.5,20C19.15,20 20.85,20.3 22.25,21.05C22.35,21.1 22.4,21.1 22.5,21.1C22.75,21.1 23,20.85 23,20.6V6C22.4,5.55 21.75,5.25 21,5M21,18.5C19.9,18.15 18.7,18 17.5,18C15.8,18 13.35,18.65 12,19.5V8C13.35,7.15 15.8,6.5 17.5,6.5C18.7,6.5 19.9,6.65 21,7V18.5Z"/>
            </svg>
          </div>
          <h3 class="value-title">{{ $t('about.values.authenticity') }}</h3>
        </div>

        <!-- الابتكار -->
        <div class="value-card" data-aos="fade-up" data-aos-delay="200">
          <div class="value-icon bg-gradient-to-br from-blue-500 to-blue-600">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M9,22A1,1 0 0,1 8,21V18H4A2,2 0 0,1 2,16V4C2,2.89 2.9,2 4,2H20A2,2 0 0,1 22,4V16A2,2 0 0,1 20,18H13.9L10.2,21.71C10,21.9 9.75,22 9.5,22V22H9M10,16V19.08L13.08,16H20V4H4V16H10M6,7H18V9H6V7M6,11H16V13H6V11Z"/>
            </svg>
          </div>
          <h3 class="value-title">{{ $t('about.values.innovation') }}</h3>
        </div>

        <!-- سهولة الوصول -->
        <div class="value-card" data-aos="fade-up" data-aos-delay="300">
          <div class="value-icon bg-gradient-to-br from-purple-500 to-purple-600">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C15.4,11.5 16,12.1 16,12.7V16.2C16,16.8 15.4,17.3 14.8,17.3H9.2C8.6,17.3 8,16.8 8,16.2V12.8C8,12.2 8.6,11.7 9.2,11.7V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.5,8.7 10.5,9.5V11.5H13.5V9.5C13.5,8.7 12.8,8.2 12,8.2Z"/>
            </svg>
          </div>
          <h3 class="value-title">{{ $t('about.values.accessibility') }}</h3>
        </div>

        <!-- الجودة -->
        <div class="value-card" data-aos="fade-up" data-aos-delay="400">
          <div class="value-icon bg-gradient-to-br from-gold-500 to-gold-600">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12,2L13.09,8.26L20,9L13.09,9.74L12,16L10.91,9.74L4,9L10.91,8.26L12,2Z"/>
            </svg>
          </div>
          <h3 class="value-title">{{ $t('about.values.quality') }}</h3>
        </div>
      </div>
    </section>

    <!-- قسم المطور -->
    <section class="container mx-auto mb-16">
      <div class="glass-card max-w-2xl mx-auto text-center" data-aos="fade-up">
        <div class="mb-6">
          <div class="w-20 h-20 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
            </svg>
          </div>
          <h2 class="text-2xl font-bold text-slate-800 dark:text-slate-200 font-arabic mb-2">
            {{ $t('about.developer.title') }}
          </h2>
          <h3 class="text-xl text-gold-600 dark:text-gold-400 font-semibold mb-4">
            {{ $t('about.developer.name') }}
          </h3>
        </div>
        <p class="text-slate-600 dark:text-slate-400 leading-relaxed mb-6">
          {{ $t('about.developer.thanks') }}
        </p>
        
        <!-- روابط التواصل -->
        <div class="flex justify-center space-x-4 rtl:space-x-reverse">
          <a href="#" class="social-link" title="GitHub">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
          </a>
          <a href="#" class="social-link" title="LinkedIn">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
            </svg>
          </a>
          <a href="#" class="social-link" title="Twitter">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
            </svg>
          </a>
        </div>
      </div>
    </section>

    <!-- آية قرآنية ختامية -->
    <section class="container mx-auto">
      <div class="glass-card max-w-3xl mx-auto text-center" data-aos="fade-up">
        <div class="mb-6">
          <svg class="w-12 h-12 mx-auto text-gold-500 mb-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"/>
          </svg>
        </div>
        <p class="verse-text font-arabic text-xl mb-4">
          ﴿ وَمَا خَلَقْتُ الْجِنَّ وَالْإِنسَ إِلَّا لِيَعْبُدُونِ ﴾
        </p>
        <p class="text-sm text-slate-500 dark:text-slate-400 mb-6">
          سورة الذاريات - الآية 56
        </p>
        <p class="text-slate-600 dark:text-slate-400 leading-relaxed">
          نسأل الله أن يتقبل منا هذا العمل وأن يجعله في ميزان حسناتنا، وأن ينفع به المسلمين في جميع أنحاء العالم
        </p>
      </div>
    </section>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

// إعدادات SEO لصفحة حولنا
useHead({
  title: 'الذِّكر الحكيم - حولنا',
  meta: [
    { name: 'description', content: 'تعرف على رسالة ورؤية موقع الذِّكر الحكيم في خدمة الإسلام والمسلمين من خلال التقنيات الحديثة' }
  ]
})

// تهيئة مكتبة AOS للأنيميشن
onMounted(() => {
  if (process.client) {
    import('aos').then((AOS) => {
      AOS.default.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true,
        offset: 100
      })
    })
  }
})
</script>

<style scoped>
.value-card {
  @apply glass-card;
  @apply text-center;
  @apply group;
  @apply transition-all duration-300;
  @apply hover:shadow-xl;
  @apply cursor-pointer;
}

.value-card:hover {
  transform: translateY(-5px) scale(1.02);
}

.value-icon {
  @apply w-16 h-16;
  @apply rounded-full;
  @apply flex items-center justify-center;
  @apply mx-auto mb-4;
  @apply group-hover:scale-110;
  @apply transition-transform duration-300;
  @apply shadow-lg;
}

.value-title {
  @apply text-lg font-semibold;
  @apply text-slate-800 dark:text-slate-200;
  @apply font-arabic;
}

.social-link {
  @apply w-12 h-12;
  @apply bg-white/10 dark:bg-slate-800/20;
  @apply hover:bg-gold-500 dark:hover:bg-gold-600;
  @apply text-slate-600 dark:text-slate-400;
  @apply hover:text-white;
  @apply rounded-full;
  @apply flex items-center justify-center;
  @apply transition-all duration-300;
  @apply transform hover:scale-110;
}

.verse-text {
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.1) 0%, 
    rgba(30, 58, 138, 0.1) 100%);
  @apply rounded-lg p-6;
}
</style>
